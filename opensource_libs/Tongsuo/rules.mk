# Copyright (C) 2024 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt

# This file is used by Trusty to build Tongsuo library using Tongsuo's native build system

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Tongsuo build configuration
TONGSUO_SRCDIR := $(LOCAL_DIR)
TONGSUO_BUILDDIR := $(BUILDDIR)/tongsuo
TONGSUO_INCDIR := $(TONGSUO_BUILDDIR)/include

# Target architecture mapping
ifeq ($(ARCH),arm64)
TONGSUO_TARGET := linux-aarch64
else ifeq ($(ARCH),arm)
TONGSUO_TARGET := linux-armv4
else
TONGSUO_TARGET := linux-generic64
endif

# Tongsuo configuration options
TONGSUO_CONFIG_OPTS := \
	--prefix=$(TONGSUO_BUILDDIR) \
	--openssldir=$(TONGSUO_BUILDDIR)/ssl \
	--symbol-prefix=TONGSUO_ \
	--api=1.1.1 \
	no-shared \
	no-apps \
	no-tests \
	no-docs \
	no-man \
	no-fuzz-afl \
	no-fuzz-libfuzzer \
	no-external-tests \
	no-ssl \
	no-tls1 \
	no-tls1_1 \
	no-tls1_2 \
	no-tls1_3 \
	no-dtls \
	no-dtls1 \
	no-dtls1_2 \
	no-engine \
	no-dynamic-engine \
	no-hw \
	no-async \
	no-sock \
	no-dgram \
	no-stdio \
	no-ui-console \
	no-autoload-config \
	enable-sm2 \
	enable-sm3 \
	enable-sm4 \
	enable-ntls

# Cross-compilation settings
TONGSUO_CC := $(CLANG_BINDIR)/clang
TONGSUO_AR := $(CLANG_BINDIR)/llvm-ar
TONGSUO_RANLIB := $(CLANG_BINDIR)/llvm-ranlib

# Compiler flags for Tongsuo
TONGSUO_CFLAGS := \
	-target $(STANDARD_ARCH_NAME)-unknown-linux-musl \
	-fno-builtin \
	-nostdlib \
	-nostdinc \
	-fPIC \
	-Os \
	-ffunction-sections \
	-fdata-sections \
	$(GLOBAL_SHARED_COMPILEFLAGS) \
	$(GLOBAL_USER_COMPILEFLAGS) \
	$(ARCH_$(ARCH)_COMPILEFLAGS)

# Include paths for musl and kernel headers
TONGSUO_INCLUDES := \
	-I$(LKMAKEROOT)/opensource_libs/musl/include \
	-I$(LKMAKEROOT)/opensource_libs/musl/arch/$(ARCH) \
	-I$(LKMAKEROOT)/opensource_libs/headers/include \
	-I$(LKMAKEROOT)/kernel/lk/include \
	-I$(LKMAKEROOT)/kernel/lk/arch/$(ARCH)/include

TONGSUO_CFLAGS += $(TONGSUO_INCLUDES)

# Configuration and build targets
TONGSUO_CONFIGURED := $(TONGSUO_BUILDDIR)/.configured
TONGSUO_BUILT := $(TONGSUO_BUILDDIR)/.built

# Configuration step
$(TONGSUO_CONFIGURED): $(TONGSUO_SRCDIR)/Configure
	@echo "Configuring Tongsuo..."
	@echo "TONGSUO_BUILDDIR: $(TONGSUO_BUILDDIR)"
	@echo "TONGSUO_TARGET: $(TONGSUO_TARGET)"
	@echo "TONGSUO_CC: $(TONGSUO_CC)"
	@mkdir -p $(TONGSUO_BUILDDIR)
	cd $(TONGSUO_SRCDIR) && \
	CC="$(TONGSUO_CC)" \
	AR="$(TONGSUO_AR)" \
	RANLIB="$(TONGSUO_RANLIB)" \
	CFLAGS="$(TONGSUO_CFLAGS)" \
	./Configure $(TONGSUO_TARGET) $(TONGSUO_CONFIG_OPTS)
	@touch $@

# Build step
$(TONGSUO_BUILT): $(TONGSUO_CONFIGURED)
	@echo "Building Tongsuo..."
	cd $(TONGSUO_SRCDIR) && \
	$(MAKE) build_libs
	@mkdir -p $(TONGSUO_INCDIR)
	@cp $(TONGSUO_SRCDIR)/libcrypto.a $(TONGSUO_BUILDDIR)/libTongsuo.a
	@cp -r $(TONGSUO_SRCDIR)/include/openssl $(TONGSUO_INCDIR)/
	@echo "Tongsuo headers exported to: $(TONGSUO_INCDIR)"
	@ls -la $(TONGSUO_INCDIR)/openssl/ | head -10
	@touch $@

# Set up library variables for Trusty build system
LIB_NAME := Tongsuo
LIB_BIN := $(TONGSUO_BUILDDIR)/libTongsuo.a
LIB_BIN_DEPS :=
LIB_SRC_DEPS :=
COMP_OUTDIR := $(TONGSUO_BUILDDIR)
COMP_OBJS :=

# Main library target
$(LIB_BIN): $(TONGSUO_BUILT)

# Module configuration for Trusty build system
MODULE_EXPORT_INCLUDES += $(TONGSUO_INCDIR)

# Force the build of Tongsuo when this module is included
$(MODULE): $(LIB_BIN)

# Add dependency to ensure headers are available during compilation
MODULE_DEPS += $(TONGSUO_BUILT)

# Clean target
.PHONY: tongsuo-clean
tongsuo-clean:
	@echo "Cleaning Tongsuo..."
	@rm -rf $(TONGSUO_BUILDDIR)
	cd $(TONGSUO_SRCDIR) && $(MAKE) clean || true

include make/rctee_lib.mk
